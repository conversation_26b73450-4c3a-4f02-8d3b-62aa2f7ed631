import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import 'package:railops/services/notification_services/onboarding_notification_service.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/services/authentication_services/jwt_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';

/// Helper class to integrate OnboardingNotificationService with existing services
/// This provides a simple interface to add notification functionality to existing API calls
class NotificationIntegrationHelper {
  static final OnboardingNotificationService _notificationService =
      OnboardingNotificationService();

  /// Enhanced version of UpcomingStationService.fetchUpcomingStationDetails
  /// that automatically processes notifications after successful API response
  static Future<UpcomingStationResponse>
      fetchUpcomingStationDetailsWithNotifications({
    required String lat,
    required String lng,
    required String token,
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications =
        false, // Disabled by default to avoid spam
  }) async {
    try {
      // Call the existing API service
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: lat,
        lng: lng,
        token: token,
      );

      // Process notifications based on the response
      // NOTE: Disabled onboarding notification processing to prevent raw data notifications
      // The Firebase Cloud Function system handles notifications properly with formatted tables
      if (false) {
        // Temporarily disabled
        await _notificationService.processUpcomingStationResponse(
          response,
          enableBoardingNotifications: enableBoardingNotifications,
          enableOffBoardingNotifications: enableOffBoardingNotifications,
          enableStationApproachingNotifications:
              enableStationApproachingNotifications,
        );

        if (kDebugMode) {
          print(
              'NotificationIntegrationHelper: Successfully processed notifications for train ${response.trainNumber}');
        }
      } else {
        if (kDebugMode) {
          print(
              'NotificationIntegrationHelper: Onboarding notification processing disabled - using Firebase Cloud Function system instead');
        }
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('NotificationIntegrationHelper: Error in enhanced fetch: $e');
      }
      rethrow; // Re-throw the original error to maintain existing error handling
    }
  }

  /// Process notifications for an existing UpcomingStationResponse
  /// Useful when you already have the response and want to trigger notifications
  /// NOTE: Disabled to prevent raw data notifications - use Firebase Cloud Function system instead
  static Future<void> processNotificationsForResponse(
    UpcomingStationResponse response, {
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications = false,
  }) async {
    // Disabled onboarding notification processing to prevent raw data notifications
    if (false) {
      // Temporarily disabled
      await _notificationService.processUpcomingStationResponse(
        response,
        enableBoardingNotifications: enableBoardingNotifications,
        enableOffBoardingNotifications: enableOffBoardingNotifications,
        enableStationApproachingNotifications:
            enableStationApproachingNotifications,
      );
    } else {
      if (kDebugMode) {
        print(
            'NotificationIntegrationHelper: processNotificationsForResponse disabled - using Firebase Cloud Function system instead');
      }
    }
  }

  /// Configure notification timing settings
  /// This allows customization of when notifications are sent
  static void configureNotificationTiming({
    Duration? boardingNotificationDelay,
    Duration? offBoardingNotificationDelay,
    int? stationsBeforeAlert,
  }) {
    OnboardingNotificationService.updateNotificationConfig(
      boardingDelay: boardingNotificationDelay,
      offBoardingDelay: offBoardingNotificationDelay,
      stationsBefore: stationsBeforeAlert,
    );
  }

  // Phase 2: Enhanced configuration methods

  /// Configure proximity-based notification settings
  static void configureProximityNotifications({
    double? proximityThresholdKm,
    Duration? locationUpdateInterval,
    bool enableLocationMonitoring = true,
  }) {
    OnboardingNotificationService.updateProximityConfig(
      proximityThreshold: proximityThresholdKm,
      updateInterval: locationUpdateInterval,
      enableMonitoring: enableLocationMonitoring,
    );
  }

  /// Configure passenger count threshold notifications
  static void configurePassengerCountNotifications({
    int? passengerCountThreshold,
    bool enableBoardingCountUpdates = true,
    bool enableOffBoardingPreparation = true,
  }) {
    OnboardingNotificationService.updatePassengerCountConfig(
      threshold: passengerCountThreshold,
      enableBoardingUpdates: enableBoardingCountUpdates,
      enableOffBoardingPrep: enableOffBoardingPreparation,
    );
  }

  /// Configure train status monitoring
  static void configureTrainStatusMonitoring({
    List<String>? monitoredStatusTypes,
    Duration? statusCheckInterval,
    bool enableScheduleChangeAlerts = true,
  }) {
    OnboardingNotificationService.updateTrainStatusConfig(
      statusTypes: monitoredStatusTypes,
      checkInterval: statusCheckInterval,
      enableScheduleAlerts: enableScheduleChangeAlerts,
    );
  }

  /// Send a test notification (useful for development and testing)
  static Future<void> sendTestNotification({
    String title = 'Test Notification',
    String body = 'This is a test notification from the onboarding system',
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: title,
      body: body,
    );
  }

  /// Get real user context for testing (replaces hardcoded test values)
  static Future<Map<String, dynamic>> getRealUserContext() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get user ID from JWT or SharedPreferences
      String? userId = await JwtService.getStoredUserId();
      userId ??= prefs.getString('user_id');

      // Get train number from SharedPreferences
      final trainNumber = prefs.getString('trainNo') ?? '';

      // Get auth token
      final authToken = prefs.getString('authToken') ?? '';

      // Get current date
      final currentDate = FirebaseCloudFunctionService.getCurrentDateString();

      // Get real GPS coordinates
      Position? position;
      try {
        // Check location permissions
        LocationPermission permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.denied) {
          permission = await Geolocator.requestPermission();
        }

        if (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always) {
          position = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
            ),
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error getting GPS location: $e');
        }
      }

      return {
        'user_id': userId,
        'train_number': trainNumber,
        'auth_token': authToken,
        'current_date': currentDate,
        'latitude':
            position?.latitude.toString() ?? '28.6139', // Fallback to New Delhi
        'longitude': position?.longitude.toString() ??
            '77.2090', // Fallback to New Delhi
        'has_real_location': position != null,
        'has_user_context':
            (userId?.isNotEmpty ?? false) && trainNumber.isNotEmpty,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting real user context: $e');
      }
      return {
        'user_id': null,
        'train_number': '',
        'auth_token': '',
        'current_date': FirebaseCloudFunctionService.getCurrentDateString(),
        'latitude': '28.6139', // Fallback to New Delhi
        'longitude': '77.2090', // Fallback to New Delhi
        'has_real_location': false,
        'has_user_context': false,
        'error': e.toString(),
      };
    }
  }

  /// Test real notification pipeline (replaces mock test notifications)
  static Future<Map<String, dynamic>> testRealNotificationPipeline({
    String? customTrainNumber,
    String? customLat,
    String? customLng,
  }) async {
    try {
      if (kDebugMode) {
        print('🧪 Testing REAL notification pipeline...');
      }

      // Get real user context
      final userContext = await getRealUserContext();

      if (!userContext['has_user_context']) {
        return {
          'success': false,
          'error': 'Missing user context',
          'details':
              'User ID or train number not found. Please ensure you are logged in and have a train assignment.',
          'user_context': userContext,
        };
      }

      final userId = userContext['user_id'] as String;
      final trainNumber =
          customTrainNumber ?? userContext['train_number'] as String;
      final lat = customLat ?? userContext['latitude'] as String;
      final lng = customLng ?? userContext['longitude'] as String;
      final currentDate = userContext['current_date'] as String;

      if (kDebugMode) {
        print('📍 Using real coordinates: $lat, $lng');
        print('🚂 Using real train: $trainNumber');
        print('👤 Using real user ID: $userId');
        print('📅 Using date: $currentDate');
      }

      // Get FCM token
      String? fcmToken;
      try {
        fcmToken = await FcmTokenService.getFcmToken();
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Warning: Could not get FCM token: $e');
        }
      }

      // Call the REAL Firebase Cloud Function /notify endpoint
      final result = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: trainNumber,
        date: currentDate,
        lat: lat,
        lng: lng,
        fcmToken: fcmToken,
      );

      if (kDebugMode) {
        print(
            '📥 Cloud Function Result: ${result['status']} - ${result['message']}');
      }

      return {
        'success': result['status'] == 'sent',
        'status': result['status'],
        'message': result['message'],
        'details': result['details'],
        'user_context': userContext,
        'request_data': {
          'user_id': userId,
          'train_number': trainNumber,
          'date': currentDate,
          'coordinates': '$lat, $lng',
          'fcm_token_available': fcmToken != null,
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error testing real notification pipeline: $e');
      }
      return {
        'success': false,
        'error': 'Pipeline test failed',
        'details': e.toString(),
      };
    }
  }

  // Phase 2: Enhanced helper methods

  /// Send a test proximity notification
  static Future<void> sendTestProximityNotification({
    String stationName = 'Test Station',
    double distanceKm = 2.5,
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: 'Approaching $stationName',
      body: 'You are ${distanceKm.toStringAsFixed(1)}km away from $stationName',
      type: OnboardingNotificationType.proximityAlert,
      additionalData: {
        'notification_category': 'proximity_alert',
        'distance_km': distanceKm,
      },
    );
  }

  /// Send a test station approach alert
  static Future<void> sendTestStationApproachAlert({
    String stationName = 'Test Station',
    int minutesBeforeArrival = 5,
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: 'Approaching $stationName',
      body: 'Next stop in $minutesBeforeArrival minutes - $stationName',
      type: OnboardingNotificationType.stationApproachAlert,
      additionalData: {
        'notification_category': 'station_approach_alert',
        'minutes_before_arrival': minutesBeforeArrival,
      },
    );
  }

  /// Send a test train status update notification
  static Future<void> sendTestTrainStatusUpdate({
    String trainNumber = 'TEST123',
    String statusType = 'delay',
    String statusMessage = 'Train delayed by 15 minutes due to signal issues',
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: 'Train $trainNumber - ${statusType.toUpperCase()}',
      body: statusMessage,
      type: OnboardingNotificationType.trainStatusUpdate,
      additionalData: {
        'notification_category': 'train_status_update',
        'status_type': statusType,
        'train_number': trainNumber,
      },
    );
  }

  /// Send a test boarding count update notification
  static Future<void> sendTestBoardingCountUpdate({
    String stationName = 'Test Station',
    int currentCount = 8,
    int previousCount = 5,
    List<String> coaches = const ['A1', 'B2'],
  }) async {
    final difference = currentCount - previousCount;
    await _notificationService.triggerImmediateNotification(
      title: 'Boarding Update - $stationName',
      body:
          '+$difference passengers boarding in coaches: ${coaches.join(', ')} (Total: $currentCount)',
      type: OnboardingNotificationType.boardingCountUpdate,
      additionalData: {
        'notification_category': 'boarding_count_update',
        'current_count': currentCount,
        'previous_count': previousCount,
        'difference': difference,
        'coaches': coaches,
      },
    );
  }

  /// Get notification service statistics
  static Map<String, dynamic> getNotificationStats() {
    return {
      'processed_notifications':
          _notificationService.processedNotificationCount,
      'boarding_delay_minutes':
          OnboardingNotificationService.boardingNotificationDelay.inMinutes,
      'offboarding_delay_minutes':
          OnboardingNotificationService.offBoardingNotificationDelay.inMinutes,
      'stations_before_alert':
          OnboardingNotificationService.stationsBeforeAlert,
    };
  }

  /// Clear notification cache (useful for testing)
  static void clearNotificationCache() {
    _notificationService.clearProcessedNotifications();
  }
}

/// Example usage class showing how to integrate with existing code
class NotificationIntegrationExample {
  /// Example 1: Replace existing UpcomingStationService calls
  static Future<void> exampleReplaceExistingCall() async {
    // Instead of:
    // final response = await UpcomingStationService.fetchUpcomingStationDetails(
    //   lat: '28.6139',
    //   lng: '77.2090',
    //   token: 'user_token',
    // );

    // Use this enhanced version:
    final response = await NotificationIntegrationHelper
        .fetchUpcomingStationDetailsWithNotifications(
      lat: '28.6139',
      lng: '77.2090',
      token: 'user_token',
      enableBoardingNotifications: true,
      enableOffBoardingNotifications: true,
      enableStationApproachingNotifications: false,
    );

    // Continue with existing logic
    if (kDebugMode) {
      print('Received response for train: ${response.trainNumber}');
    }
  }

  /// Example 2: Process notifications for existing response
  static Future<void> exampleProcessExistingResponse(
      UpcomingStationResponse response) async {
    // If you already have a response and want to add notifications:
    await NotificationIntegrationHelper.processNotificationsForResponse(
      response,
      enableBoardingNotifications: true,
      enableOffBoardingNotifications: true,
    );
  }

  /// Example 3: Configure notification timing
  static void exampleConfigureNotifications() {
    // Customize when notifications are sent
    NotificationIntegrationHelper.configureNotificationTiming(
      boardingNotificationDelay:
          const Duration(minutes: 10), // 10 minutes before boarding
      offBoardingNotificationDelay:
          const Duration(minutes: 20), // 20 minutes before off-boarding
      stationsBeforeAlert: 1, // Alert 1 station before
    );
  }

  /// Example 4: Testing notifications
  static Future<void> exampleTestNotifications() async {
    // Send a test notification
    await NotificationIntegrationHelper.sendTestNotification(
      title: 'Test Alert',
      body: 'Testing the notification system',
    );

    // Check statistics
    final stats = NotificationIntegrationHelper.getNotificationStats();
    if (kDebugMode) {
      print('Notification stats: $stats');
    }
  }
}
