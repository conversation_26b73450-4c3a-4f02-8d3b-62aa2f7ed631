import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import axios from "axios";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Types for API responses
interface TrainLocationData {
  train_number: string;
  date: string;
  stations: Array<{
    station_code: string;
    station_name: string;
    coaches: Array<{
      coach_number: string;
      onboarding_count: number;
      off_boarding_count: number;
      vacant_count: number;
    }>;
  }>;
}

interface NotificationPayload {
  title: string;
  body: string;
  data: {
    type: string;
    station_code: string;
    coach_data: string;
    timestamp: string;
  };
}

// Enhanced types for new functionality
interface UserCoachAssignment {
  out?: { [key: string]: string[] }; // outward journey coach assignments
  in?: { [key: string]: string[] };  // return journey coach assignments
  has_access: boolean;
}

interface StationNotificationState {
  station_code: string;
  coach_data: { [coach: string]: [number, number, number] }; // [onboarding, off_boarding, vacant]
  last_notified: string; // ISO timestamp
  notification_count: number;
}

/**
 * Get FCM token for a specific user from Firestore
 */
const getUserFcmToken = async (userId: string): Promise<string | null> => {
  try {
    const doc = await admin.firestore().collection("tokens").doc(userId).get();
    const data = doc.data();
    return data?.fcm_token || null;
  } catch (error) {
    console.error(`Error fetching FCM token for user ${userId}:`, error);
    return null;
  }
};

/**
 * Get user's coach assignments for a specific train and date
 */
const getUserCoachAssignments = async (
  userId: string,
  trainNumber: string,
  date: string,
  bearerToken: string
): Promise<UserCoachAssignment | null> => {
  try {
    const response = await axios.post(
      "https://railops-uat-api.biputri.com/api/users/get_Train_access_coach_wise/",
      {
        train_number: trainNumber,
        date: date,
        for_user_type: "coach attendent", // This might need to be dynamic based on user type
        token: bearerToken,
      },
      {
        headers: {
          "Authorization": `Bearer ${bearerToken}`,
          "Content-Type": "application/json",
        },
        timeout: 10000,
      }
    );

    console.log(`Coach assignments for user ${userId}:`, JSON.stringify(response.data, null, 2));
    return response.data as UserCoachAssignment;
  } catch (error) {
    console.error(`Error fetching coach assignments for user ${userId}:`, error);
    return null;
  }
};

// Legacy functions removed to clean up unused code
// calculateDistance, isAlertAlreadySent, markAlertAsSent functions
// have been replaced by enhanced station-level notification tracking

/**
 * Get station notification state from Firestore
 */
const getStationNotificationState = async (
  userId: string,
  trainNumber: string,
  date: string,
  stationCode: string
): Promise<StationNotificationState | null> => {
  try {
    const docId = `${userId}/${trainNumber}/${date}/${stationCode}`;
    const doc = await admin.firestore().collection("stationNotifications").doc(docId).get();

    if (doc.exists) {
      const data = doc.data();
      return {
        station_code: data?.station_code || stationCode,
        coach_data: data?.coach_data || {},
        last_notified: data?.last_notified || "",
        notification_count: data?.notification_count || 0,
      };
    }
    return null;
  } catch (error) {
    console.error(`Error getting station notification state for ${stationCode}:`, error);
    return null;
  }
};

/**
 * Check if station notification should be sent based on change detection
 */
const shouldSendStationNotification = async (
  userId: string,
  trainNumber: string,
  date: string,
  stationCode: string,
  currentCoachData: { [coach: string]: [number, number, number] }
): Promise<boolean> => {
  try {
    const existingState = await getStationNotificationState(userId, trainNumber, date, stationCode);

    // If no previous state, send notification
    if (!existingState) {
      console.log(`No previous state for station ${stationCode}, sending notification`);
      return true;
    }

    // Check if coach data has changed
    const hasChanged = Object.keys(currentCoachData).some(coach => {
      const currentData = currentCoachData[coach];
      const previousData = existingState.coach_data[coach];

      if (!previousData) return true; // New coach data

      return (
        currentData[0] !== previousData[0] || // onboarding count changed
        currentData[1] !== previousData[1] || // off_boarding count changed
        currentData[2] !== previousData[2]    // vacant count changed
      );
    });

    if (hasChanged) {
      console.log(`Coach data changed for station ${stationCode}, sending notification`);
      return true;
    }

    console.log(`No changes detected for station ${stationCode}, skipping notification`);
    return false;
  } catch (error) {
    console.error(`Error checking station notification state for ${stationCode}:`, error);
    return true; // Send notification on error to be safe
  }
};

/**
 * Update station notification state in Firestore
 */
const updateStationNotificationState = async (
  userId: string,
  trainNumber: string,
  date: string,
  stationCode: string,
  coachData: { [coach: string]: [number, number, number] }
): Promise<void> => {
  try {
    const docId = `${userId}/${trainNumber}/${date}/${stationCode}`;
    const existingState = await getStationNotificationState(userId, trainNumber, date, stationCode);

    await admin.firestore().collection("stationNotifications").doc(docId).set({
      station_code: stationCode,
      coach_data: coachData,
      last_notified: new Date().toISOString(),
      notification_count: (existingState?.notification_count || 0) + 1,
      updated_at: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating station notification state for ${stationCode}:`, error);
  }
};

/**
 * Fetch train location data from microservice
 */
const fetchTrainLocationData = async (
  trainNumber: string,
  date: string,
  bearerToken: string
): Promise<TrainLocationData | null> => {
  try {
    console.log(`🔍 [API_DEBUG] Fetching train location data for train: ${trainNumber}, date: ${date}`);

    const response = await axios.get(
      "https://railops-uat-api.biputri.com/microservice/train/location/",
      {
        params: {
          train_number: trainNumber,
          date: date,
        },
        headers: {
          "Authorization": `Bearer ${bearerToken}`,
          "Content-Type": "application/json",
        },
        timeout: 10000, // 10 second timeout
      }
    );

    console.log(`🔍 [API_DEBUG] Response status: ${response.status}`);
    console.log("🔍 [API_DEBUG] Response headers:", JSON.stringify(response.headers, null, 2));

    // Log the actual API response for debugging with detailed analysis
    console.log("🔍 [API_DEBUG] Raw API response:", JSON.stringify(response.data, null, 2));
    console.log(`🔍 [API_DEBUG] Response data type: ${typeof response.data}`);
    console.log("🔍 [API_DEBUG] Response data keys:", Object.keys(response.data || {}));

    // Validate response structure
    const data = response.data;
    if (!data || typeof data !== "object") {
      console.error("❌ [API_ERROR] Invalid API response: data is not an object");
      console.error("❌ [API_ERROR] Received data:", data);
      return null;
    }

    // Analyze the actual structure of the response
    console.log("🔍 [API_DEBUG] Analyzing response structure:");
    for (const [key, value] of Object.entries(data)) {
      console.log(`🔍 [API_DEBUG] Key: ${key}, Value type: ${typeof value}, Value:`, JSON.stringify(value, null, 2));

      if (typeof value === "object" && value !== null) {
        console.log(`🔍 [API_DEBUG] ${key} sub-keys:`, Object.keys(value));

        // Analyze each sub-key
        for (const [subKey, subValue] of Object.entries(value)) {
          console.log(`🔍 [API_DEBUG] ${key}.${subKey} type: ${typeof subValue}, value:`, JSON.stringify(subValue, null, 2));

          if (Array.isArray(subValue)) {
            console.log(`🔍 [API_DEBUG] ${key}.${subKey} is array with length: ${subValue.length}`);
            if (subValue.length > 0) {
              console.log(`🔍 [API_DEBUG] ${key}.${subKey} first element:`, JSON.stringify(subValue[0], null, 2));
            }
          }
        }
      }
    }

    // Transform the API response to match our expected format
    // Expected format: { "BWN": { "D3": [5, 3, 2], "S7": [1, 0, 4] }, "NJP": { "C1": [2, 1, 3] } }
    // We need: { stations: [{ station_code: "BWN", coaches: [...] }] }

    const transformedData: TrainLocationData = {
      train_number: trainNumber,
      date: date,
      stations: []
    };

    let totalStationsProcessed = 0;
    let totalCoachesProcessed = 0;
    let transformationErrors = 0;

    // Convert station data to our expected format
    for (const [stationCode, coachData] of Object.entries(data)) {
      console.log(`🔍 [TRANSFORM_DEBUG] Processing station: ${stationCode}`);

      if (typeof coachData === "object" && coachData !== null) {
        const coaches = [];
        totalStationsProcessed++;

        // Convert coach data to our expected format
        for (const [coachNumber, passengerData] of Object.entries(coachData)) {
          console.log(`🔍 [TRANSFORM_DEBUG] Processing coach: ${coachNumber}, data:`, JSON.stringify(passengerData, null, 2));

          try {
            if (Array.isArray(passengerData)) {
              const onboarding = passengerData.length > 0 ? (passengerData[0] || 0) : 0;
              const offBoarding = passengerData.length > 1 ? (passengerData[1] || 0) : 0;
              const vacant = passengerData.length > 2 ? (passengerData[2] || 0) : 0;

              coaches.push({
                coach_number: coachNumber,
                onboarding_count: onboarding,
                off_boarding_count: offBoarding,
                vacant_count: vacant,
              });

              console.log(`✅ [TRANSFORM_DEBUG] Coach ${coachNumber}: onboarding=${onboarding}, off_boarding=${offBoarding}, vacant=${vacant}`);
              totalCoachesProcessed++;
            } else if (typeof passengerData === "object" && passengerData !== null) {
              // Handle case where passenger data is an object instead of array
              console.log(`🔍 [TRANSFORM_DEBUG] Coach ${coachNumber} has object data, attempting to extract counts`);

              const onboarding = passengerData.onboarding_count || passengerData.onboarding || 0;
              const offBoarding = passengerData.off_boarding_count || passengerData.off_boarding || 0;
              const vacant = passengerData.vacant_count || passengerData.vacant || 0;

              coaches.push({
                coach_number: coachNumber,
                onboarding_count: onboarding,
                off_boarding_count: offBoarding,
                vacant_count: vacant,
              });

              console.log(`✅ [TRANSFORM_DEBUG] Coach ${coachNumber} (object): onboarding=${onboarding}, off_boarding=${offBoarding}, vacant=${vacant}`);
              totalCoachesProcessed++;
            } else {
              // Handle case where coach data is not an array or object
              console.log(`⚠️ [TRANSFORM_WARNING] Coach ${coachNumber} has unexpected data type: ${typeof passengerData}`);
              coaches.push({
                coach_number: coachNumber,
                onboarding_count: 0,
                off_boarding_count: 0,
                vacant_count: 0,
              });
              transformationErrors++;
            }
          } catch (coachError) {
            console.error(`❌ [TRANSFORM_ERROR] Error processing coach ${coachNumber}:`, coachError);
            transformationErrors++;
          }
        }

        if (coaches.length > 0) {
          transformedData.stations.push({
            station_code: stationCode,
            station_name: stationCode, // Use station code as name for now
            coaches: coaches
          });
          console.log(`✅ [TRANSFORM_DEBUG] Station ${stationCode} added with ${coaches.length} coaches`);
        } else {
          console.log(`⚠️ [TRANSFORM_WARNING] Station ${stationCode} has no valid coaches`);
        }
      } else {
        console.log(`⚠️ [TRANSFORM_WARNING] Station ${stationCode} has invalid coach data type: ${typeof coachData}`);
        transformationErrors++;
      }
    }

    console.log(`🔍 [TRANSFORM_SUMMARY] Processed ${totalStationsProcessed} stations, ${totalCoachesProcessed} coaches, ${transformationErrors} errors`);
    console.log("🔍 [TRANSFORM_DEBUG] Final transformed data:", JSON.stringify(transformedData, null, 2));

    if (transformedData.stations.length === 0) {
      console.error("❌ [TRANSFORM_ERROR] No stations were successfully transformed");
      return null;
    }

    return transformedData;
  } catch (error: any) {
    console.error("❌ [API_ERROR] Error fetching train location data:", error);

    if (error.response) {
      console.error("❌ [API_ERROR] Response status:", error.response.status);
      console.error("❌ [API_ERROR] Response data:", JSON.stringify(error.response.data, null, 2));
      console.error("❌ [API_ERROR] Response headers:", JSON.stringify(error.response.headers, null, 2));
    } else if (error.request) {
      console.error("❌ [API_ERROR] No response received:", error.request);
    } else {
      console.error("❌ [API_ERROR] Request setup error:", error.message);
    }

    return null;
  }
};

/**
 * Filter coach data based on user assignments
 * If user has no assignments, return all stations with a special marker
 */
const filterCoachesByUserAssignment = (
  stations: TrainLocationData["stations"],
  userAssignments: UserCoachAssignment | null,
  hasCoachAssignments: boolean
): TrainLocationData["stations"] => {
  // If user has no coach assignments, return all stations for general train information
  if (!hasCoachAssignments || !userAssignments || !userAssignments.has_access) {
    console.log("User has no coach assignments, returning all stations for general train information");
    return stations;
  }

  // Get all assigned coaches (combine outward and return journey assignments)
  const assignedCoaches = new Set<string>();

  if (userAssignments.out) {
    Object.values(userAssignments.out).forEach(coaches => {
      coaches.forEach(coach => assignedCoaches.add(coach));
    });
  }

  if (userAssignments.in) {
    Object.values(userAssignments.in).forEach(coaches => {
      coaches.forEach(coach => assignedCoaches.add(coach));
    });
  }

  console.log(`User assigned coaches: ${Array.from(assignedCoaches).join(", ")}`);

  // Filter stations to only include assigned coaches
  return stations.map(station => ({
    ...station,
    coaches: station.coaches.filter(coach => assignedCoaches.has(coach.coach_number))
  })).filter(station => station.coaches.length > 0); // Remove stations with no assigned coaches
};

/**
 * Build enhanced coach table string for notification body with table format
 * Handles both users with coach assignments and users without assignments
 */
const buildEnhancedCoachTable = (
  stations: TrainLocationData["stations"],
  hasCoachAssignments: boolean
): string => {
  console.log(`🔍 [TABLE_DEBUG] Building coach table for ${stations?.length || 0} stations, hasCoachAssignments: ${hasCoachAssignments}`);

  // Validate stations array
  if (!Array.isArray(stations)) {
    console.error("❌ [TABLE_ERROR] stations is not an array:", typeof stations, stations);
    return hasCoachAssignments
      ? "Error: Invalid station data format"
      : "Train update for stations - data format error. No coach assignments - contact admin for access.";
  }

  if (stations.length === 0) {
    console.log("⚠️ [TABLE_WARNING] stations array is empty");
    return hasCoachAssignments
      ? "No stations found"
      : "Train update - no stations available. No coach assignments - contact admin for access.";
  }

  // Log detailed station information for debugging
  console.log("🔍 [TABLE_DEBUG] Station details:");
  stations.forEach((station, index) => {
    console.log(`🔍 [TABLE_DEBUG] Station ${index}: code=${station.station_code}, coaches=${station.coaches?.length || 0}`);
    if (station.coaches && station.coaches.length > 0) {
      station.coaches.forEach((coach, coachIndex) => {
        console.log(`🔍 [TABLE_DEBUG]   Coach ${coachIndex}: ${coach.coach_number} - onboarding=${coach.onboarding_count}, off_boarding=${coach.off_boarding_count}, vacant=${coach.vacant_count}`);
      });
    }
  });

  // If user has no coach assignments, provide general train information
  if (!hasCoachAssignments) {
    console.log("🔍 [TABLE_DEBUG] Building general train information for user without assignments");

    const stationCodes = stations.map(s => s.station_code).join(", ");
    let totalOnboarding = 0;
    let totalOffBoarding = 0;
    let totalVacant = 0;
    let totalCoaches = 0;
    let validStations = 0;

    stations.forEach(station => {
      if (station.coaches && Array.isArray(station.coaches)) {
        let stationHasData = false;
        station.coaches.forEach(coach => {
          if (coach && typeof coach === "object") {
            totalOnboarding += coach.onboarding_count || 0;
            totalOffBoarding += coach.off_boarding_count || 0;
            totalVacant += coach.vacant_count || 0;
            totalCoaches++;
            stationHasData = true;
          }
        });
        if (stationHasData) validStations++;
      }
    });

    console.log(`🔍 [TABLE_DEBUG] General summary: ${validStations} valid stations, ${totalCoaches} coaches, ${totalOnboarding} boarding, ${totalOffBoarding} deboarding, ${totalVacant} vacant`);

    if (validStations === 0) {
      const fallbackMessage = "Train update - no valid station data available. No coach assignments - contact admin for access.";
      console.log(`⚠️ [TABLE_WARNING] No valid stations found, returning: ${fallbackMessage}`);
      return fallbackMessage;
    }

    if (totalOnboarding === 0 && totalOffBoarding === 0 && totalVacant === 0) {
      const noActivityMessage = `Train update for station${stations.length > 1 ? "s" : ""} ${stationCodes}. No coach assignments - contact admin for access.`;
      console.log(`🔍 [TABLE_DEBUG] No activity detected, returning: ${noActivityMessage}`);
      return noActivityMessage;
    }

    const summaryMessage = `Train update for ${stationCodes}: ${totalOnboarding} boarding, ${totalOffBoarding} deboarding, ${totalVacant} vacant across ${totalCoaches} coaches. No coach assignments - contact admin for access.`;
    console.log(`✅ [TABLE_DEBUG] Generated general summary: ${summaryMessage}`);
    return summaryMessage;
  }

  console.log("🔍 [TABLE_DEBUG] Building detailed coach table for user with assignments");

  // Check if there's any passenger activity for users with assignments
  const hasActivity = stations.some(station =>
    station.coaches && Array.isArray(station.coaches) && station.coaches.some(coach =>
      coach && typeof coach === "object" &&
      (coach.onboarding_count > 0 || coach.off_boarding_count > 0 || coach.vacant_count > 0)
    )
  );

  console.log(`🔍 [TABLE_DEBUG] Activity check result: ${hasActivity}`);

  if (!hasActivity) {
    const stationCodes = stations.map(s => s.station_code).join(", ");
    const noActivityMessage = `No passenger onboarding/deboarding at station${stations.length > 1 ? "s" : ""} ${stationCodes}.`;
    console.log(`🔍 [TABLE_DEBUG] No activity for assigned user, returning: ${noActivityMessage}`);
    return noActivityMessage;
  }

  // Build table format: StationCode | Coach | Onboarding | De-boarding | Vacant
  let table = "Station | Coach | Board | Deboard | Vacant\n";
  table += "--------|-------|-------|---------|-------\n";

  let tableRows = 0;
  let totalDataPoints = 0;

  stations.forEach(station => {
    if (station.coaches && Array.isArray(station.coaches)) {
      station.coaches.forEach(coach => {
        if (coach && typeof coach === "object") {
          const onboarding = coach.onboarding_count || 0;
          const offBoarding = coach.off_boarding_count || 0;
          const vacant = coach.vacant_count || 0;

          // Only include rows with activity for users with assignments
          if (onboarding > 0 || offBoarding > 0 || vacant > 0) {
            table += `${station.station_code} | ${coach.coach_number} | ${onboarding} | ${offBoarding} | ${vacant}\n`;
            tableRows++;
            totalDataPoints += onboarding + offBoarding + vacant;
          }
        }
      });
    }
  });

  console.log(`🔍 [TABLE_DEBUG] Generated table with ${tableRows} rows, ${totalDataPoints} total data points`);

  // Ensure the result is not too long for FCM (limit to ~300 characters)
  if (table.length > 300) {
    console.log(`⚠️ [TABLE_WARNING] Table too long (${table.length} chars), creating summary instead`);

    // Create a summary instead
    let totalOnboarding = 0;
    let totalOffBoarding = 0;
    let totalVacant = 0;
    let coachCount = 0;

    stations.forEach(station => {
      if (station.coaches && Array.isArray(station.coaches)) {
        station.coaches.forEach(coach => {
          if (coach && typeof coach === "object") {
            totalOnboarding += coach.onboarding_count || 0;
            totalOffBoarding += coach.off_boarding_count || 0;
            totalVacant += coach.vacant_count || 0;
            coachCount++;
          }
        });
      }
    });

    const stationCodes = stations.map(s => s.station_code).join(", ");
    const summary = `${stationCodes}: ${totalOnboarding} boarding, ${totalOffBoarding} deboarding, ${totalVacant} vacant (${coachCount} coaches)`;
    console.log(`🔍 [TABLE_DEBUG] Generated summary: ${summary}`);
    return summary;
  }

  if (tableRows === 0) {
    console.log("⚠️ [TABLE_WARNING] No valid table rows generated");
    const noDataMessage = "No coach data available for assigned coaches";
    console.log(`🔍 [TABLE_DEBUG] Returning no data message: ${noDataMessage}`);
    return noDataMessage;
  }

  console.log(`✅ [TABLE_DEBUG] Final table generated successfully (${table.length} chars)`);
  return table;
};

// Legacy buildCoachTable function removed - using buildEnhancedCoachTable directly

/**
 * Send FCM notification with custom sound
 */
const sendFcmNotification = async (
  fcmToken: string,
  payload: NotificationPayload
): Promise<boolean> => {
  try {
    const message = {
      token: fcmToken,
      notification: {
        title: payload.title,
        body: payload.body,
      },
      data: payload.data,
      android: {
        notification: {
          channelId: "railops_alerts",
          sound: "railops_alarm",
          priority: "high" as const,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: "railops_alarm.caf",
            category: "RAILOPS_ALERT",
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log("FCM message sent successfully:", response);
    return true;
  } catch (error) {
    console.error("Error sending FCM message:", error);
    return false;
  }
};

/**
 * Main Cloud Function: POST /notify endpoint
 * Fetches train location data, builds coach tables, and sends FCM notifications
 */
export const notify = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (req.method === "OPTIONS") {
    res.status(200).send();
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed. Use POST." });
    return;
  }

  try {
    // Extract request parameters
    const { user_id, train_number, date, lat, lng } = req.body;

    // Validate required parameters
    if (!user_id || !train_number || !date) {
      res.status(400).json({
        error: "Missing required parameters: user_id, train_number, date",
      });
    }

    // Log coordinates if provided (for future proximity-based features)
    if (lat && lng) {
      console.log(`Request coordinates: lat=${lat}, lng=${lng}`);
    }

    console.log(`Processing notification request for user: ${user_id}, train: ${train_number}, date: ${date}`);

    // Log invocation details for monitoring
    console.log(`[NOTIFY] Invocation - User: ${user_id}, Train: ${train_number}, Date: ${date}, Coordinates: ${lat || "N/A"},${lng || "N/A"}`);

    // Get RAILOPS_BEARER secret
    const bearerToken = functions.config().railops?.bearer;
    if (!bearerToken) {
      console.error("RAILOPS_BEARER secret not configured");
      res.status(500).json({ error: "Server configuration error" });
      return;
    }

    // Get user's FCM token
    const fcmToken = await getUserFcmToken(user_id);
    if (!fcmToken) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No FCM token found`);
      res.status(404).json({ error: "User FCM token not found" });
      return;
    }

    // Get user's coach assignments (but don't skip users without assignments)
    const userAssignments = await getUserCoachAssignments(user_id, train_number, date, bearerToken);
    const hasCoachAssignments = Boolean(userAssignments && userAssignments.has_access);

    if (!hasCoachAssignments) {
      console.log(`[NOTIFY] User: ${user_id} has no coach assignments for train ${train_number}, but will still receive general train notifications`);
    }

    // Fetch train location data
    const trainData = await fetchTrainLocationData(train_number, date, bearerToken);
    if (!trainData) {
      console.log(`[NOTIFY] Status: ERROR, User: ${user_id}, Station: N/A - Failed to fetch train location data for ${train_number}`);
      res.status(500).json({ error: "Failed to fetch train location data" });
      return;
    }

    // Filter stations based on user assignments (or include all if no assignments)
    const filteredStations = filterCoachesByUserAssignment(trainData.stations, userAssignments, hasCoachAssignments);

    if (filteredStations.length === 0) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No station data available in train location response`);
      res.status(200).json({
        status: "skipped",
        message: "No station data available in current train location response",
        user_id: user_id,
        train_number: train_number,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Check station-level notifications and change detection
    const stationsToNotify = [];
    for (const station of filteredStations) {
      // Convert coach data to the format expected by shouldSendStationNotification
      const coachData: { [coach: string]: [number, number, number] } = {};
      station.coaches.forEach(coach => {
        coachData[coach.coach_number] = [
          coach.onboarding_count,
          coach.off_boarding_count,
          coach.vacant_count
        ];
      });

      // For users without coach assignments, we still want to check for changes
      // but we'll use a simplified approach since they don't have specific coaches
      const shouldNotify = hasCoachAssignments
        ? await shouldSendStationNotification(user_id, train_number, date, station.station_code, coachData)
        : await shouldSendStationNotification(user_id, train_number, date, station.station_code, coachData);

      if (shouldNotify) {
        stationsToNotify.push(station);
        // Update the notification state for this station
        await updateStationNotificationState(
          user_id,
          train_number,
          date,
          station.station_code,
          coachData
        );
      }
    }

    if (stationsToNotify.length === 0) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No changes detected in coach data`);
      res.status(200).json({
        status: "skipped",
        message: "No changes detected in coach data",
        user_id: user_id,
        train_number: train_number,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Build enhanced coach table for notification
    const coachTable = buildEnhancedCoachTable(stationsToNotify, hasCoachAssignments);

    // Create enhanced notification payload with appropriate title based on assignment status
    const stationCodes = stationsToNotify.map(s => s.station_code).join(", ");
    const notificationTitle = hasCoachAssignments
      ? `Train ${train_number} - Station Updates`
      : `Train ${train_number} - General Update`;

    const notificationPayload: NotificationPayload = {
      title: notificationTitle,
      body: coachTable,
      data: {
        type: "enhanced_train_location_update",
        station_code: stationCodes,
        coach_data: JSON.stringify({
          stations_count: stationsToNotify.length,
          stations: stationsToNotify.map(s => s.station_code),
          summary: coachTable,
          has_coach_assignments: hasCoachAssignments
        }),
        timestamp: new Date().toISOString(),
      },
    };

    // Send FCM notification
    const notificationSent = await sendFcmNotification(fcmToken, notificationPayload);
    if (!notificationSent) {
      console.log(`[NOTIFY] Status: ERROR, User: ${user_id}, Station: ${stationCodes} - Failed to send FCM notification`);
      res.status(500).json({ error: "Failed to send notification" });
      return;
    }

    const notificationTypeMsg = hasCoachAssignments ? "Enhanced notification" : "General train notification";
    console.log(`[NOTIFY] Status: SENT, User: ${user_id}, Station: ${stationCodes} - ${notificationTypeMsg} sent successfully`);
    res.status(200).json({
      status: "sent",
      message: `${notificationTypeMsg} sent successfully`,
      user_id: user_id,
      train_number: train_number,
      stations_notified: stationsToNotify.map(s => s.station_code),
      stations_count: stationsToNotify.length,
      total_coaches: stationsToNotify.reduce((sum, station) => sum + station.coaches.length, 0),
      has_coach_assignments: hasCoachAssignments,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error in notify function:", error);
    res.status(500).json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});
